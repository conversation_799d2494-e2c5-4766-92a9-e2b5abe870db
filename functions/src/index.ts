import { genkit, z } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { onCallGenkit } from 'firebase-functions/https';
import { defineSecret } from 'firebase-functions/params';

const apiKey = defineSecret('GEMINI_API_KEY');

const ai = genkit({
    plugins: [googleAI()],
    model: googleAI.model('gemini-2.5-flash'),
});

const generatePoemFlow = ai.defineFlow(
    {
        name: 'generatePoem',
        inputSchema: z.object({ subject: z.string() }),
        outputSchema: z.object({ poem: z.string() }),
    },
    async ({ subject }) => {
        const { text } = await ai.generate(`Compose a poem about ${subject}.`);
        return { poem: text };
    },
);

export const generatePoem = onCallGenkit(
    {
        secrets: [apiKey],
        // authPolicy: hasClaim('email_verified'),
        // enforceAppCheck: true,
    },
    generatePoemFlow,
);
